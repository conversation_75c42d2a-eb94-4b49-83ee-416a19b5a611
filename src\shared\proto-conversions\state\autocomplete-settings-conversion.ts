import { AutocompleteSettings } from "@shared/AutocompleteSettings"
import { AutocompleteSettings as ProtoAutocompleteSettings, FimSettings as ProtoFimSettings } from "../../../shared/proto/state"

/**
 * Converts domain AutocompleteSettings objects to proto AutocompleteSettings objects
 */
export function convertAutocompleteSettingsToProtoAutocompleteSettings(
	settings: AutocompleteSettings,
): ProtoAutocompleteSettings {
	return ProtoAutocompleteSettings.create({
		enabled: settings.enabled,
		provider: settings.provider,
		apiKey: settings.apiKey,
		apiBaseUrl: settings.apiBaseUrl,
		modelId: settings.modelId,
		maxTokens: settings.maxTokens,
		temperature: settings.temperature,
		requestTimeoutMs: settings.requestTimeoutMs ? Number(settings.requestTimeoutMs) : undefined,
		usePromptCache: settings.usePromptCache,
		customHeaders: settings.customHeaders ? JSON.stringify(settings.customHeaders) : undefined,
		debounceMs: settings.debounceMs,
		fim: settings.fim
			? ProtoFimSettings.create({
					apiKey: settings.fim.apiKey,
					baseUrl: settings.fim.baseUrl,
				})
			: undefined,
	})
}

/**
 * Converts proto AutocompleteSettings objects to domain AutocompleteSettings objects
 */
export function convertProtoAutocompleteSettingsToAutocompleteSettings(
	protoSettings: ProtoAutocompleteSettings,
): AutocompleteSettings {
	// eslint-disable-next-line eslint-rules/no-protobuf-object-literals
	const settings: AutocompleteSettings = {
		enabled: protoSettings.enabled,
		provider: protoSettings.provider as "openai" | "fim" | undefined,
		apiKey: protoSettings.apiKey,
		apiBaseUrl: protoSettings.apiBaseUrl,
		modelId: protoSettings.modelId,
		maxTokens: protoSettings.maxTokens,
		temperature: protoSettings.temperature,
		requestTimeoutMs: protoSettings.requestTimeoutMs ? Number(protoSettings.requestTimeoutMs) : undefined,
		usePromptCache: protoSettings.usePromptCache,
		debounceMs: protoSettings.debounceMs,
		fim: protoSettings.fim
			? {
					apiKey: protoSettings.fim.apiKey,
					baseUrl: protoSettings.fim.baseUrl,
				}
			: undefined,
	}

	// Handle custom headers JSON parsing
	try {
		if (protoSettings.customHeaders) {
			settings.customHeaders = JSON.parse(protoSettings.customHeaders)
		}
	} catch (error) {
		console.error("Failed to parse custom headers in autocomplete settings:", error)
		settings.customHeaders = {}
	}

	return settings
}
